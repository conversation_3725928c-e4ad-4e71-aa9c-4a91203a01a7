import {
  Ri<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ine,
  Ri<PERSON>rrowUpDownLine,
  RiCloseLargeLine,
  Ri<PERSON><PERSON>2<PERSON>ill,
  RiLockUnlockFill,
} from "@remixicon/react";
import Highlight from "@tiptap/extension-highlight";
import Placeholder from "@tiptap/extension-placeholder";
import Table from "@tiptap/extension-table";
import TableHeader from "@tiptap/extension-table-header";
import TableRow from "@tiptap/extension-table-row";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import { useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import CustomTiptapEditor from "components/Common/CustomTiptapEditor";
import EnhancedToolbar from "components/Common/CustomTiptapEditor/EnhancedToolbar";
import ContentLock from "components/Common/CustomTiptapEditor/extensions/ContentLock";
import GraphPlaceholder from "components/Common/CustomTiptapEditor/extensions/GraphPlaceholder";
import { ImageResize } from "components/Common/CustomTiptapEditor/extensions/ImageResize";
import ImageUploadNode from "components/Common/CustomTiptapEditor/extensions/ImageUploadNode/imageUploadNodeExtension";
import {
  useDeleteReportAsset,
  useGetReportAssetMutation,
} from "features/Reports/api";
import {
  removeReportBlockAtIndex,
  swapReportBlocks,
  updateReportBlockContent,
  updateReportBlockLockState,
  updateReportBlockTitle,
} from "features/Reports/store";
import useReportStore from "features/Reports/store/report";
import { useEffect, useRef, useState } from "react";
import { Button } from "react-bootstrap";
import { setConfirmModalConfig } from "stores";
import { useSectionLock } from "../hooks/useSectionLock";
import {
  createImageUploadHandler,
  MAX_FILE_SIZE,
  useUploadReportAsset,
} from "../hooks/useUploadReportAsset";

import CustomTableCell from "components/Common/CustomTiptapEditor/extensions/CustomTableCell";
import TabIndent from "components/Common/CustomTiptapEditor/extensions/TabIndent";
import { REPORTS_ROUTE_PATH } from "features/Reports/routePath";
import { useLocation } from "react-router-dom";
import InsertSectionModal from "../InsertSectionModal";
import GenerateReportToolbar from "./GenerateReportToolbar";
import SelectionLockButton from "./SelectionLockButton";
import "./styles.scss";

const baseExtensions = [
  StarterKit,
  Highlight,
  Underline,
  Table.configure({
    resizable: true,
  }),
  TableRow,
  TableHeader,
  CustomTableCell,
  GraphPlaceholder,
  ContentLock,
  Placeholder.configure({
    placeholder: "Start writing here...",
    emptyEditorClass: "is-editor-empty",
    showOnlyWhenEditable: true,
    showOnlyCurrent: true,
  }),
  TextAlign.configure({
    types: ["heading", "paragraph"],
  }),
];

interface ContentStructure {
  type: string;
  content: string;
}

interface SectionBlockProps {
  title: string;
  content: ContentStructure | string;
  index: number;
  is_locked?: boolean;
  id?: string;
  is_confirmed?: boolean;
  saveReportSilenty?: any;
  source_section_id?: number;
}

const assetUrlRequestCache = new Map<string, Promise<string>>();

const SectionBlock = ({
  title,
  content,
  index,
  is_locked = false,
  id: sectionId,
  is_confirmed = false,
  saveReportSilenty,
  source_section_id,
}: SectionBlockProps) => {
  const [showInsertModal, setShowInsertModal] = useState(false);
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [localTitle, setLocalTitle] = useState(title);
  const [hovered, setHovered] = useState(false);

  const reportInfo = useReportStore((state) => state.reportInfo);
  const titleRef = useRef<HTMLInputElement>(null);
  const location = useLocation();

  const { uploadAsset } = useUploadReportAsset({
    onError: (error: Error) => console.error("Upload failed:", error),
  });

  const { mutateAsync: deleteAsset } = useDeleteReportAsset();
  const { mutateAsync: getAsset } = useGetReportAssetMutation();

  const handleUrlRefresh = async (
    assetId: string,
    onSuccess: (newUrl: string) => void
  ) => {
    if (!assetId) return;

    try {
      if (!assetUrlRequestCache.has(assetId)) {
        const request = getAsset(assetId).then((response) => {
          const url = response?.data?.asset?.url;
          if (!url) throw new Error("No URL returned from asset refresh");
          return url;
        });

        assetUrlRequestCache.set(assetId, request);
      }

      const newUrl: any = await assetUrlRequestCache.get(assetId);
      onSuccess(newUrl);
      setTimeout(
        () => {
          assetUrlRequestCache.delete(assetId);
        },
        5 * 60 * 1000
      );
    } catch (error) {
      console.error("Failed to refresh asset URL:", error);
      assetUrlRequestCache.delete(assetId);
    }
  };

  const handleDeleteAsset = async ({ pos, assetId, editor }: any) => {
    if (assetId) {
      try {
        const result: any = await deleteAsset({ assetId });
        if (result?.success) {
          editor
            .chain()
            .deleteRange({ from: pos, to: pos + 1 })
            .run();
          saveReportSilenty && saveReportSilenty({ saveSilently: true });
        }
      } catch (error) {
        console.error("Failed to delete asset:", error);
        throw error;
      }
    }
  };

  const handleDeleteAssetWithModal = async ({ pos, assetId, editor }: any) => {
    if (!assetId) return;

    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => handleDeleteAsset({ pos, assetId, editor }),
        onClose: () => {
          // Do nothing on close - this fixes the modal issue
        },
        content: {
          heading: "Delete Asset",
          description: "Are you sure you want to delete this asset?",
        },
        buttonText: "Delete",
      },
    });
  };

  const extensions = [
    ...baseExtensions,
    ImageResize.configure({
      onDelete: handleDeleteAssetWithModal,
      onUrlRefresh: handleUrlRefresh,
    }),
    ImageUploadNode.configure({
      accept: "image/*",
      maxSize: MAX_FILE_SIZE,
      limit: 3,
      upload: createImageUploadHandler(uploadAsset),
      onError: (error: any) => console.error("Upload failed:", error),
      onDelete: handleDeleteAssetWithModal,
      showDeleteAction: true,
    }),
    TabIndent,
  ];

  const getInitialContent = () => {
    if (
      typeof content === "object" &&
      content !== null &&
      content.type === "html"
    ) {
      return content.content;
    } else if (typeof content === "string") {
      return content;
    }
    return "";
  };

  const editor = useEditor({
    extensions,
    content: getInitialContent(),
    editable: is_confirmed ? false : true,
  });

  const { lockState, toggleSectionLock } = useSectionLock({
    editor,
    sectionIndex: index,
    isSectionLocked: is_locked,
    onSectionLockChange: updateReportBlockLockState,
  });

  useEffect(() => {
    setLocalTitle(title);
  }, [title]);

  useEffect(() => {
    if (editor && content !== undefined) {
      try {
        if (typeof content === "object" && content !== null) {
          // Handle new structured format: { type: "html", content: "..." }
          if (content.type === "html" && content.content) {
            if (content.content !== editor.getHTML()) {
              editor.commands.setContent(content.content);
            }
          } else {
            // Handle legacy JSON content for backward compatibility (TipTap JSON format)
            const currentContent = editor.getJSON();
            if (
              JSON.stringify(content) !== JSON.stringify(currentContent) &&
              content?.content?.length
            ) {
              editor.commands.setContent(content as any);
            }
          }
        } else if (typeof content === "string") {
          // Handle legacy HTML string format
          if (content !== editor.getHTML()) {
            editor.commands.setContent(content);
          }
        }
      } catch (error) {
        console.error("Error updating editor content:", error);
      }
    }
  }, [editor, content]);

  // Auto-save content on editor changes
  useEffect(() => {
    if (!editor) return;

    const handleUpdate = () => {
      const currentContent = editor.getHTML();
      const structuredContent = {
        type: "html",
        content: currentContent,
      };
      updateReportBlockContent(index, structuredContent);
    };

    editor.on("update", handleUpdate);

    return () => {
      editor.off("update", handleUpdate);
    };
  }, [editor, index]);

  useEffect(() => {
    if (editor) {
      editor.setEditable(is_confirmed ? false : true);

      // Force node view updates by triggering a state update
      // This ensures all node views (especially ImageResize) are aware of the editable state change
      const currentState = editor.state;
      const transaction = currentState.tr;

      // Create a minimal transaction that forces node view updates
      // by updating the document without changing content
      editor.view.dispatch(transaction);

      // Also force a content refresh to ensure all node views are properly updated
      setTimeout(() => {
        const json = editor.getJSON();
        editor.commands.setContent(json, false);
      }, 0);
    }
  }, [editor, is_confirmed, sectionId]);

  const handleTitleClick = () => {
    if (is_confirmed) return;
    setIsEditingTitle(true);
    setTimeout(() => {
      if (titleRef.current) {
        titleRef.current.focus();
        titleRef.current.select();
      }
    }, 0);
  };

  const handleTitleSave = () => {
    if (titleRef.current) {
      const newTitle = titleRef.current.value.trim();
      if (newTitle && newTitle !== title) {
        updateReportBlockTitle(index, newTitle);
        setLocalTitle(newTitle);
      } else if (!newTitle) {
        setLocalTitle(title);
      }
      setIsEditingTitle(false);
    }
  };

  const handleTitleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleTitleSave();
    } else if (e.key === "Escape") {
      setLocalTitle(title);
      setIsEditingTitle(false);
    }
  };

  const handleTitleBlur = () => {
    handleTitleSave();
  };

  const handleRemoveSection = () => {
    if (reportInfo.sections.length > 1) {
      removeReportBlockAtIndex(index);
    }
  };

  const onClickRemove = () => {
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: handleRemoveSection,
        content: {
          heading: "Remove Section",
          description: "Are you sure you want to remove this section?",
        },
      },
    });
  };

  const handleReorderSection = () => {
    const nextIndex = index + 1;
    const prevIndex = index - 1;

    if (nextIndex < reportInfo.sections.length) {
      swapReportBlocks(index, nextIndex);
    } else if (prevIndex >= 0) {
      swapReportBlocks(index, prevIndex);
    }
  };

  const isGenerateSection = location.pathname.includes(
    REPORTS_ROUTE_PATH.GENERATE_REPORT
  );

  return (
    <>
      <div className="section-block mb-4 p-3 rounded">
        <div className="section-header d-flex justify-content-between align-items-center mb-2">
          <div
            className={`title d-flex align-items-center gap-1 ${is_confirmed ? "pe-none" : ""}`}
          >
            <Button
              variant=""
              className={`section-lock-btn ${lockState.isFullyLocked || is_locked ? "locked" : lockState.isPartiallyLocked ? "partial" : "unlocked"}`}
              onClick={() => !is_confirmed && toggleSectionLock()}
              title={
                lockState.isFullyLocked || is_locked
                  ? "Section fully locked - click to unlock all"
                  : lockState.isPartiallyLocked
                    ? "Section partially locked - click to lock all"
                    : "Section unlocked - click to lock all"
              }
            >
              {lockState.isFullyLocked || is_locked ? (
                <RiLock2Fill color="#ad986f" />
              ) : (
                <RiLockUnlockFill color="#ad986f" />
              )}
            </Button>
            {isEditingTitle && !is_confirmed ? (
              <input
                ref={titleRef}
                type="text"
                className="section-title-input fw-bold"
                defaultValue={localTitle}
                onKeyDown={handleTitleKeyDown}
                onBlur={handleTitleBlur}
                autoFocus
              />
            ) : (
              <h5
                className="mb-0 fw-bold section-title-display"
                onClick={handleTitleClick}
                title="Click to edit title"
              >
                {localTitle}
              </h5>
            )}
          </div>
          <div className="controls">
            <Button
              variant=""
              onClick={() => setShowInsertModal(true)}
              title="Insert section"
              className="p-0 px-1"
            >
              <RiAddLargeLine color="#ad986f" />
            </Button>
            {reportInfo.sections.length > 1 && (
              <>
                <Button
                  variant=""
                  onClick={handleReorderSection}
                  title="Reorder section"
                  className="p-0 px-1"
                >
                  <RiArrowUpDownLine color="#ad986f" />
                </Button>
                <Button
                  variant=""
                  onClick={onClickRemove}
                  title="Remove section"
                  className="p-0 px-1"
                >
                  <RiCloseLargeLine color="#ad986f" />
                </Button>
              </>
            )}
            {isGenerateSection && (
              <GenerateReportToolbar
                editor={editor}
                index={index}
                sectionId={sectionId}
                source_section_id={source_section_id}
              />
            )}
          </div>
        </div>
        <div
          className="section-content position-relative"
          onMouseEnter={() => setHovered(true)}
          onMouseLeave={() => setHovered(false)}
        >
          {!is_confirmed && (
            <EnhancedToolbar editor={editor} sectionId={sectionId} />
          )}

          {is_confirmed && hovered && (
            <div
              className="position-absolute top-0 start-50 translate-middle-x px-3 py-1"
              style={{
                backgroundColor: "#ad986f",
                color: "#fff",
                fontWeight: 500,
                borderRadius: "4px",
                boxShadow: "0 1px 4px rgba(0,0,0,0.1)",
                zIndex: 10,
              }}
            >
              Section is locked. Unconfirm to edit.
            </div>
          )}

          <div className="editor-container position-relative">
            <CustomTiptapEditor editor={editor} />
            {!(lockState.isFullyLocked || is_locked) && !is_confirmed && (
              <SelectionLockButton editor={editor} />
            )}
          </div>
        </div>
      </div>
      {showInsertModal && (
        <InsertSectionModal
          show={showInsertModal}
          onClose={() => setShowInsertModal(false)}
          index={index}
        />
      )}
    </>
  );
};

export default SectionBlock;
