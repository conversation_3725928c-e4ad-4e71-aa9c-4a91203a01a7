import useReportStore from "features/Reports/store/report";
import { useAutoSave } from "hooks";
import HeaderBar from "./HeaderBar";
import SectionBlock from "./SectionBlock";
import "./styles.scss";

interface ReportBuilderProps {
  onSaveReport: any;
  children?: React.ReactNode;
  saveReportSilenty: any;
}

const ReportBuilder = ({
  onSaveReport,
  children = <></>,
  saveReportSilenty,
}: ReportBuilderProps) => {
  const reportInfo: any = useReportStore((state) => state.reportInfo);
  const { title, sections } = reportInfo || {};

  const sortedSections = sections?.sort(
    (a: any, b: any) => a.position - b.position
  );

  useAutoSave({
    onAutoSave: saveReportSilenty,
    autoSaveArgs: { saveSilently: true },
  });

  return (
    <div className="report-builder-section bg-white h-100 w-100 rounded p-3 overflow-auto">
      <HeaderBar title={title} onSaveReport={onSaveReport} />
      {sortedSections.map((block: any, idx: number) => (
        <SectionBlock
          key={`${reportInfo.id || 'new'}-${idx}-${block.id || block.position}`}
          {...block}
          index={idx}
          saveReportSilenty={saveReportSilenty}
        />
      ))}
      {children}
    </div>
  );
};

export default ReportBuilder;
